"use client";

import { Button1 } from "@/app/_components/shared/Button1";
import { Button, useMediaQuery } from "@relume_io/relume-ui";
import { AnimatePresence, motion } from "framer-motion";
import { usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { RxChevronDown } from "react-icons/rx";


const useRelume = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("NL");
  const isMobile = useMediaQuery("(max-width: 991px)");
  const toggleMobileMenu = () => setIsMobileMenuOpen((prev) => !prev);
  const openOnMobileDropdownMenu = () => {
    setIsDropdownOpen((prev) => !prev);
  };
  const openOnDesktopDropdownMenu = () => {
    !isMobile && setIsDropdownOpen(true);
  };
  const closeOnDesktopDropdownMenu = () => {
    !isMobile && setIsDropdownOpen(false);
  };
  const openOnMobileLanguageDropdownMenu = () => {
    setIsLanguageDropdownOpen((prev) => !prev);
  };
  const openOnDesktopLanguageDropdownMenu = () => {
    !isMobile && setIsLanguageDropdownOpen(true);
  };
  const closeOnDesktopLanguageDropdownMenu = () => {
    !isMobile && setIsLanguageDropdownOpen(false);
  };
  const selectLanguage = (language) => {
    setSelectedLanguage(language);
    setIsLanguageDropdownOpen(false);
  };
  const animateMobileMenu = isMobileMenuOpen ? "open" : "close";
  const animateMobileMenuButtonSpan = isMobileMenuOpen
    ? ["open", "rotatePhase"]
    : "closed";
  const animateDropdownMenu = isDropdownOpen ? "open" : "close";
  const animateDropdownMenuIcon = isDropdownOpen ? "rotated" : "initial";
  const animateLanguageDropdown = isLanguageDropdownOpen ? "open" : "close";
  const animateLanguageDropdownIcon = isLanguageDropdownOpen ? "rotated" : "initial";
  return {
    toggleMobileMenu,
    openOnDesktopDropdownMenu,
    closeOnDesktopDropdownMenu,
    openOnMobileDropdownMenu,
    openOnMobileLanguageDropdownMenu,
    openOnDesktopLanguageDropdownMenu,
    closeOnDesktopLanguageDropdownMenu,
    selectLanguage,
    selectedLanguage,
    animateMobileMenu,
    animateMobileMenuButtonSpan,
    animateDropdownMenu,
    animateDropdownMenuIcon,
    animateLanguageDropdown,
    animateLanguageDropdownIcon,
  };
};

export function Navbar2() {
  const useActive = useRelume();
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);

  // Determine if we're on the Story Forward homepage
  const isHomepage = pathname === '/storyforward';

  // For non-homepage pages, we want a static white background
  const shouldUseTransparentBehavior = isHomepage;

  // Check if mobile menu is open
  const isMobileMenuOpen = useActive.animateMobileMenu === 'open';

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      // Immediate sticky behavior - no delay
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      {/* Spacer div for non-homepage pages to account for fixed navbar */}
      {!shouldUseTransparentBehavior && (
        <div className="h-16 md:h-18 lg:h-18" />
      )}
      <section
        id="relume"
        className={`fixed top-0 left-0 right-0 w-full z-[999] flex items-center transition-all duration-300 lg:min-h-18 lg:px-[5%] ${
          shouldUseTransparentBehavior
            ? (isScrolled || isMobileMenuOpen ? 'bg-background shadow-lg' : 'bg-transparent')
            : 'bg-background shadow-lg'
        }`}
      >
      <div className="mx-auto size-full lg:grid lg:grid-cols-[0.375fr_1fr_0.375fr] lg:items-center lg:justify-between lg:gap-4">
        <div className="flex min-h-16 items-center justify-between px-[5%] md:min-h-18 lg:min-h-full lg:px-0">
          <a href="/storyforward">
            <img
              src="/brand/storyforward/full_logo.png"
              alt="Story Forward Logo"
              className="h-12 md:h-12 lg:h-14 w-auto object-contain"
            />
          </a>
          <div className="flex items-center gap-4 lg:hidden">
            <div>
              <Button
                className="w-full px-6 py-2 bg-link text-text-alternative hover:bg-link-primary rounded-lg transition-colors border-none"
                title="Let's talk!"
                size="sm"
              >
                Let's talk!
              </Button>
            </div>
            <button
              className="-mr-2 flex size-12 flex-col items-center justify-center lg:hidden"
              onClick={useActive.toggleMobileMenu}
            >
              <motion.span
                className={`my-[3px] h-0.5 w-6 transition-colors ${
                  shouldUseTransparentBehavior
                    ? (isScrolled || isMobileMenuOpen ? 'bg-text-primary' : 'bg-white')
                    : 'bg-text-primary'
                }`}
                animate={useActive.animateMobileMenuButtonSpan}
                variants={{
                  open: { translateY: 8, transition: { delay: 0.1 } },
                  rotatePhase: { rotate: -45, transition: { delay: 0.2 } },
                  closed: {
                    translateY: 0,
                    rotate: 0,
                    transition: { duration: 0.2 },
                  },
                }}
              />
              <motion.span
                className={`my-[3px] h-0.5 w-6 transition-colors ${
                  shouldUseTransparentBehavior
                    ? (isScrolled || isMobileMenuOpen ? 'bg-text-primary' : 'bg-white')
                    : 'bg-text-primary'
                }`}
                animate={useActive.animateMobileMenu}
                variants={{
                  open: { width: 0, transition: { duration: 0.1 } },
                  closed: {
                    width: "1.5rem",
                    transition: { delay: 0.3, duration: 0.2 },
                  },
                }}
              />
              <motion.span
                className={`my-[3px] h-0.5 w-6 transition-colors ${
                  shouldUseTransparentBehavior
                    ? (isScrolled || isMobileMenuOpen ? 'bg-text-primary' : 'bg-white')
                    : 'bg-text-primary'
                }`}
                animate={useActive.animateMobileMenuButtonSpan}
                variants={{
                  open: { translateY: -8, transition: { delay: 0.1 } },
                  rotatePhase: { rotate: 45, transition: { delay: 0.2 } },
                  closed: {
                    translateY: 0,
                    rotate: 0,
                    transition: { duration: 0.2 },
                  },
                }}
              />
            </button>
          </div>
        </div>
        <motion.div
          variants={{
            open: { height: "var(--height-open, 100dvh)" },
            close: { height: "var(--height-closed, 0)" },
          }}
          animate={useActive.animateMobileMenu}
          initial="close"
          exit="close"
          transition={{ duration: 0.4 }}
          className="overflow-hidden px-[5%] text-center lg:flex lg:items-center lg:justify-center lg:px-0 lg:[--height-closed:auto] lg:[--height-open:auto]"
        >
          <div
            onMouseEnter={useActive.openOnDesktopDropdownMenu}
            onMouseLeave={useActive.closeOnDesktopDropdownMenu}
          >
            <button
              className={`flex w-full items-center justify-center gap-4 py-3 text-center text-md lg:w-auto lg:flex-none lg:justify-start lg:gap-2 lg:px-4 lg:py-2 lg:text-base transition-colors ${
                shouldUseTransparentBehavior
                  ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                  : 'text-text-primary hover:text-link'
              }`}
              onClick={useActive.openOnMobileDropdownMenu}
            >
              <span>Diensten</span>
              <motion.span
                variants={{ rotated: { rotate: 180 }, initial: { rotate: 0 } }}
                animate={useActive.animateDropdownMenuIcon}
                transition={{ duration: 0.3 }}
              >
                <RxChevronDown />
              </motion.span>
            </button>
            <AnimatePresence>
              <motion.nav
                variants={{
                  open: {
                    visibility: "visible",
                    opacity: "var(--opacity-open, 100%)",
                    display: "block",
                    y: 0,
                  },
                  close: {
                    visibility: "hidden",
                    opacity: "var(--opacity-close, 0)",
                    display: "none",
                    y: "var(--y-close, 0%)",
                  },
                }}
                animate={useActive.animateDropdownMenu}
                initial="close"
                exit="close"
                transition={{ duration: 0.2 }}
                className="bg-background border border-border-primary rounded-xl lg:absolute lg:z-50 lg:p-2 lg:[--y-close:25%] lg:shadow-lg"
              >
                <a
                  href="/storyforward/strategische-communicatie"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Strategische communicatie
                </a>
                <a
                  href="/storyforward/storytelling"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Storytelling
                </a>
                <a
                  href="/storyforward/mediarelaties"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Mediarelaties
                </a>
                <a
                  href="/storyforward/trainingen-workshops"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Trainingen & workshops
                </a>
                <a
                  href="/storyforward/design-branding-web"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Design, branding & web development
                </a>
              </motion.nav>
            </AnimatePresence>
          </div>
          <a
            href="/storyforward/cases"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Cases
          </a>
          <a
            href="/storyforward/visie"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Visie
          </a>
          <a
            href="/over-ons"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Over ons
          </a>
          <a
            href="/blogs"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Blogs
          </a>
          <a
            href="/storyforward/newsroom"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Newsroom
          </a>
          <a
            href="/contact"
            className={`block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 transition-colors ${
              shouldUseTransparentBehavior
                ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                : 'text-text-primary hover:text-link'
            }`}
          >
            Contact
          </a>
          {/* Mobile Language Dropdown */}
          <div className="lg:hidden">
            <button
              className={`flex w-full items-center justify-center gap-4 py-3 text-center text-md lg:w-auto lg:flex-none lg:justify-start lg:gap-2 lg:px-4 lg:py-2 lg:text-base transition-colors ${
                shouldUseTransparentBehavior
                  ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                  : 'text-text-primary hover:text-link'
              }`}
              onClick={useActive.openOnMobileLanguageDropdownMenu}
            >
              <span>{useActive.selectedLanguage}</span>
              <motion.span
                variants={{ rotated: { rotate: 180 }, initial: { rotate: 0 } }}
                animate={useActive.animateLanguageDropdownIcon}
                transition={{ duration: 0.3 }}
              >
                <RxChevronDown />
              </motion.span>
            </button>
            <AnimatePresence>
              <motion.nav
                variants={{
                  open: {
                    visibility: "visible",
                    opacity: "var(--opacity-open, 100%)",
                    display: "block",
                    y: 0,
                  },
                  close: {
                    visibility: "hidden",
                    opacity: "var(--opacity-close, 0)",
                    display: "none",
                    y: "var(--y-close, 0%)",
                  },
                }}
                animate={useActive.animateLanguageDropdown}
                initial="close"
                exit="close"
                transition={{ duration: 0.2 }}
                className="bg-background border border-border-primary rounded-xl lg:absolute lg:z-50 lg:p-2 lg:[--y-close:25%] lg:shadow-lg"
              >
                <button
                  onClick={() => useActive.selectLanguage("NL")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  NL
                </button>
                <button
                  onClick={() => useActive.selectLanguage("EN")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  EN
                </button>
                <button
                  onClick={() => useActive.selectLanguage("FR")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  FR
                </button>
              </motion.nav>
            </AnimatePresence>
          </div>
        </motion.div>
        <div className="hidden justify-self-end lg:flex lg:items-center lg:gap-4">
          {/* Language Dropdown */}
          <div
            onMouseEnter={useActive.openOnDesktopLanguageDropdownMenu}
            onMouseLeave={useActive.closeOnDesktopLanguageDropdownMenu}
          >
            <button
              className={`flex items-center gap-2 px-3 py-2 transition-colors ${
                shouldUseTransparentBehavior
                  ? (isScrolled || isMobileMenuOpen ? 'text-text-primary hover:text-link' : 'text-white hover:text-background-secondary')
                  : 'text-text-primary hover:text-link'
              }`}
              onClick={useActive.openOnMobileLanguageDropdownMenu}
            >
              <span>{useActive.selectedLanguage}</span>
              <motion.span
                variants={{ rotated: { rotate: 180 }, initial: { rotate: 0 } }}
                animate={useActive.animateLanguageDropdownIcon}
                transition={{ duration: 0.3 }}
              >
                <RxChevronDown />
              </motion.span>
            </button>
            <AnimatePresence>
              <motion.nav
                variants={{
                  open: {
                    visibility: "visible",
                    opacity: "var(--opacity-open, 100%)",
                    display: "block",
                    y: 0,
                  },
                  close: {
                    visibility: "hidden",
                    opacity: "var(--opacity-close, 0)",
                    display: "none",
                    y: "var(--y-close, 0%)",
                  },
                }}
                animate={useActive.animateLanguageDropdown}
                initial="close"
                exit="close"
                transition={{ duration: 0.2 }}
                className="bg-background border border-border-primary rounded-xl lg:absolute lg:z-50 lg:p-2 lg:[--y-close:25%] lg:shadow-lg"
              >
                <button
                  onClick={() => useActive.selectLanguage("NL")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  NL
                </button>
                <button
                  onClick={() => useActive.selectLanguage("EN")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  EN
                </button>
                <button
                  onClick={() => useActive.selectLanguage("FR")}
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg w-full"
                >
                  FR
                </button>
              </motion.nav>
            </AnimatePresence>
          </div>
          <Button1 
            href="/contact"
            variant="link"
            title="Let's talk!" 
            size="sm">
            Let's talk!
          </Button1>
        </div>
      </div>
    </section>
    </>
  );
}
