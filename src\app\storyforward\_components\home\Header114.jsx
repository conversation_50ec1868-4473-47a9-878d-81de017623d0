"use client";

import { Button1 } from "@/app/_components/shared/Button1";
import { Button } from "@relume_io/relume-ui";
import React, { useEffect, useRef, useState } from "react";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('second-section');
    if (nextSection) {
      nextSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const sectionRect = sectionRef.current.getBoundingClientRect();
        const sectionBottom = sectionRect.bottom;
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        // Calculate parallax offset (subtle movement - 20% of scroll speed)
        const sectionTop = sectionRect.top + scrollY;
        const scrollProgress = Math.max(0, scrollY - sectionTop);
        const parallaxSpeed = 0.2; // Adjust this value for more/less parallax effect
        const currentParallaxOffset = scrollProgress * parallaxSpeed;
        setParallaxOffset(currentParallaxOffset);

        // When the section bottom reaches the bottom of the viewport, disable fixed background
        if (sectionBottom <= viewportHeight) {
          if (isFixed) {
            // Store the final parallax offset when transitioning from fixed to absolute
            setFinalParallaxOffset(currentParallaxOffset);
          }
          setIsFixed(false);
          // Calculate where the background should be positioned to maintain continuity
          const sectionHeight = sectionRef.current.offsetHeight;
          setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
        } else {
          setIsFixed(true);
          setBackgroundTop(0);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isFixed]);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} w-full -z-50`}
        style={{
          height: '200vh', // Very large height to ensure complete coverage
          top: isFixed ? '-50vh' : `${backgroundTop - (window.innerHeight * 0.5)}px`,
          left: 0,
          right: 0,
          backgroundImage: "url('/images/storyforward/homepage/5.png')",
          backgroundSize: 'cover',
          backgroundPosition: `center ${50 + (isFixed ? -parallaxOffset/10 : -finalParallaxOffset/10)}%`,
          backgroundRepeat: "no-repeat",
          backgroundAttachment: 'scroll' // Change to scroll for better mobile compatibility
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8">
                Together we walk the talk<br></br>and talk the walk!
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button1
                  title="Button"
                  variant="filled"
                  onClick={scrollToNextSection}
                >
                  Ontdek meer
                </Button1>
                <Button1
                  title="Button"
                  href="/contact"
                  variant="transparent"
                >
                  Contacteer ons
                </Button1>
              </div>
            </div>
          </div>

          {/* Second content section */}
          <div className="flex flex-col justify-center min-h-screen py-20" id="second-section">
            <div className="w-full max-w-2xl pl-[5%] md:pl-[40%] pr-[5%]">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Vertel als bedrijf wat je doet én waarom je dat doet. Zo vergroot je je impact.
              </h1>
              <p className="text-text-alternative md:text-md ">
                Door te vertellen wat je doet, én waarom, vergroot je je impact. Dat is de reden waarom we bij Story Forward jouw maatschappelijk verhaal en je duurzame inspanningen zo graag tastbaar maken met communicatie die helder, inspirerend en geloofwaardig is.<br></br><br></br>
Cijfers en feiten? Absoluut. Maar de echte kracht, die zit zeker ook in het verhaal erachter. En daar blinken wij in uit. Of het nu gaat om strategische duurzaamheidsrapporten, originele persacties of doordachte communicatiecampagnes in woord en beeld, wij denken met je mee en helpen je zelfs jouw duurzame ambities om te zetten in actie.
              </p>
              <Button1
                  title="Button"
                  href="/contact"
                  variant="transparent"
                  className="mt-8"
                >
                  Contacteer ons
                </Button1>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}