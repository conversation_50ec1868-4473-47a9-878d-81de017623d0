"use client";

import { motion } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import { Button1 } from "../shared/Button1";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('second-section');
    if (nextSection) {
      nextSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const sectionRect = sectionRef.current.getBoundingClientRect();
        const sectionBottom = sectionRect.bottom;
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        // Calculate parallax offset (subtle movement - 20% of scroll speed)
        const sectionTop = sectionRect.top + scrollY;
        const scrollProgress = Math.max(0, scrollY - sectionTop);
        const parallaxSpeed = 0.2; // Adjust this value for more/less parallax effect
        const currentParallaxOffset = scrollProgress * parallaxSpeed;
        setParallaxOffset(currentParallaxOffset);

        // When the section bottom reaches the bottom of the viewport, disable fixed background
        if (sectionBottom <= viewportHeight) {
          if (isFixed) {
            // Store the final parallax offset when transitioning from fixed to absolute
            setFinalParallaxOffset(currentParallaxOffset);
          }
          setIsFixed(false);
          // Calculate where the background should be positioned to maintain continuity
          const sectionHeight = sectionRef.current.offsetHeight;
          setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
        } else {
          setIsFixed(true);
          setBackgroundTop(0);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isFixed]);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} w-full -z-50`}
        style={{
          height: '200vh', // Very large height to ensure complete coverage
          top: isFixed ? '-50vh' : `${backgroundTop - (window.innerHeight * 0.5)}px`,
          left: 0,
          right: 0,
          backgroundImage: "url('/images/forestforward/homepage/9.png')",
          backgroundSize: 'cover',
          backgroundPosition: `center ${50 + (isFixed ? -parallaxOffset/10 : -finalParallaxOffset/10)}%`,
          backgroundRepeat: "no-repeat",
          backgroundAttachment: 'scroll' // Change to scroll for better mobile compatibility
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <motion.h1
                className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                Walk the talk, zeggen ze.<br></br> Wij doen het.
              </motion.h1>
              <motion.div
                className="mt-6 flex flex-wrap gap-4 md:mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Button1
                  title="Button"
                  variant="filled"
                  onClick={scrollToNextSection}
                >
                  Ontdek meer
                </Button1>
                <Button1
                  title="Button"
                  variant="transparent"
                  href="/contact"
                >
                  Contacteer ons
                </Button1>
              </motion.div>
            </div>
          </div>

          {/* Second content section */}
          <motion.div
            id="second-section"
            className="flex flex-col justify-center min-h-screen py-20"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, amount: 0.3 }}
          >
            <div className="w-full max-w-2xl pl-[5%] md:pl-[40%] pr-[5%]">
              <motion.h1
                className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
              >
                Samen maken we<br></br> duurzaamheid tastbaar.
              </motion.h1>
              <motion.p
                className="text-text-alternative md:text-md"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven
                groot en klein. En dan hebben we het over duurzaamheid in al z'n
                vormen. Over impact maken. Op alle mogelijke manieren. Dat is de
                meerwaarde van ons ecosysteem. We rollen de mouwen op om samen
                lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken.
                Om jouw bedrijf een stem te geven, zodat je anderen kan
                inspireren. En om de betrokkenheid van jouw stakeholders te
                vergroten door ze slim én leuk te verbinden. Zorgen voor
                daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat
                is wat we doen!
              </motion.p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}