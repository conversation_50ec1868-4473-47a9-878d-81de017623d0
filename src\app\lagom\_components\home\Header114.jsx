"use client";

import { Button1 } from "@/app/_components/shared/Button1";
import { Button } from "@relume_io/relume-ui";
import React, { useEffect, useRef, useState, useCallback } from "react";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('second-section');
    if (nextSection) {
      nextSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Throttled scroll handler for better performance
  const handleScroll = useCallback(() => {
    if (!sectionRef.current) return;

    const sectionRect = sectionRef.current.getBoundingClientRect();
    const sectionBottom = sectionRect.bottom;
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Calculate parallax offset - reduce effect on mobile for better performance
    const sectionTop = sectionRect.top + scrollY;
    const scrollProgress = Math.max(0, scrollY - sectionTop);
    const parallaxSpeed = isMobile ? 0.1 : 0.3; // Reduced speed on mobile, increased on desktop
    const currentParallaxOffset = scrollProgress * parallaxSpeed;

    // Use a smaller buffer and handle transitions more smoothly
    const transitionBuffer = 2;

    // When the section bottom reaches the bottom of the viewport, disable fixed background
    if (sectionBottom <= viewportHeight + transitionBuffer) {
      if (isFixed) {
        // Store the current parallax offset when transitioning from fixed to absolute
        setFinalParallaxOffset(currentParallaxOffset);
      }
      setIsFixed(false);
      // Calculate where the background should be positioned to maintain continuity
      const sectionHeight = sectionRef.current.offsetHeight;
      setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
    } else if (sectionBottom > viewportHeight + transitionBuffer) {
      // Only transition back to fixed if we're clearly above the threshold
      if (!isFixed) {
        // When transitioning back to fixed, reset the parallax offset smoothly
        setParallaxOffset(currentParallaxOffset);
      }
      setIsFixed(true);
      setBackgroundTop(0);
    }

    // Always update parallax offset when in fixed mode
    if (isFixed || sectionBottom > viewportHeight + transitionBuffer) {
      setParallaxOffset(currentParallaxOffset);
    }
  }, [isFixed, isMobile]);

  useEffect(() => {
    // Throttle scroll events for better performance
    let ticking = false;

    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Initial call
    handleScroll();

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, [handleScroll]);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} w-full -z-50`}
        style={{
          height: '200vh', // Very large height to ensure complete coverage
          top: isFixed ? '-50vh' : `${backgroundTop - (window.innerHeight * 0.5)}px`,
          left: 0,
          right: 0,
          backgroundImage: "url('/images/lagom/homepage/8.png')",
          backgroundSize: 'cover',
          // Enhanced parallax calculation with smooth transition - reversed direction
          backgroundPosition: `center ${50 + (isFixed ? parallaxOffset/5 : finalParallaxOffset/5)}%`,
          backgroundRepeat: "no-repeat",
          backgroundAttachment: 'scroll', // Better mobile compatibility
          // Add transform for additional parallax effect on desktop - reversed direction
          transform: !isMobile && isFixed ? `translateY(${-parallaxOffset * 0.5}px)` :
                     !isMobile && !isFixed ? `translateY(${-finalParallaxOffset * 0.5}px)` : 'none',
          // Smooth transition only when switching between fixed and absolute
          transition: 'none',
          willChange: 'transform, background-position' // Optimize for animations
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8">
                Laten we van duurzaamheid<br></br>een feest maken
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button1
                  title="Button"
                  variant="filled"
                  onClick={scrollToNextSection}
                >
                  Ontdek meer
                </Button1>
                <Button1
                  title="Button"
                  href="/contact"
                  variant="transparent"
                >
                  Contacteer ons
                </Button1>
              </div>
            </div>
          </div>

          {/* Second content section */}
          <div className="flex flex-col justify-center min-h-screen py-20" id="second-section">
            <div className="w-full max-w-2xl pl-[5%] md:pl-[40%] pr-[5%]">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Samen maken we<br></br> duurzaamheid tastbaar.
              </h1>
              <p className="text-text-alternative md:text-md ">
                Bij Lagom brengen we mensen samen voor impactvolle ervaringen. Lagom is het geheim van Zweden, de reden waarom ze er zo gelukkig zijn. Het betekent: niet te veel, niet te weinig, gewoon goed. Het draait om balans.<br></br><br></br>

Die duurzame balans creëren we samen met jou en je collega’s in tal van maatschappelijk relevante activiteiten. Van teambuildings over familiedagen tot inspirerende workshops: échte verbinding én positieve verandering is wat we nastreven. Jij geniet van het moment, wij regelen de rest.
              </p>
              <Button1
                title="Button"
                href="/contact"
                variant="transparent"
                className="mt-8"
              >
                Contacteer ons
              </Button1>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}